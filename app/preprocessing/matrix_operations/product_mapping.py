"""Product mapping functions.

This module contains functions for creating product ID mappings using asyncio.
"""

import os
import gc
import asyncio
import numpy as np
import pandas as pd
import pickle as pkl
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from contextdb.Engine.base_database import BaseDatabaseEngine

# Import SQL queries
from ..queries.product_mapping_queries import (
    PRODUCT_CATEGORIES_FOR_PROCESSING_QUERY,
    ATTRIBUTE_ORDER_BY_PCAT_QUERY,
    ATTRIBUTE_TERM_COUNT_BY_PCAT_QUERY,
    ALL_PRODUCTS_BY_CATEGORY_QUERY,
    MEASURE_DATA_WITH_ORDERING_QUERY_LEGACY,
    TERM_DATA_WITH_ORDERING_QUERY,
)


def save_product_mapping(pcat_id, indices_pid, dir):
    """Save product mapping to a pickle file.

    Args:
        pcat_id: Product category ID
        indices_pid: Tuple of (indices, product_ids)
        dir: Directory to save the file
    """
    from app.utils.logging import setup_logging

    logger = setup_logging(component_name="product_mapping")
    log = logger.bind(product_category_id=pcat_id, directory=dir)

    try:
        filepath = os.path.join(dir, str(pcat_id) + ".pkl")
        log.debug("Saving product mapping", filepath=filepath)

        with open(filepath, "wb") as f:
            pkl.dump(indices_pid, f)

        # Clean up memory
        del indices_pid
        gc.collect()

        log.debug("Product mapping saved successfully")
    except Exception as e:
        log.error("Failed to save product mapping", error=str(e), exc_info=True)
        raise


def create_product_id_mapping(
    product_category_id: int,
    local_sqlite_db: "BaseDatabaseEngine",
    attributes_by_category_table: str,
    logger,
) -> pd.Series:  # sourcery skip: low-code-quality
    """Create product ID mapping using legacy queries and logic."""
    log = logger.bind(product_category_id=product_category_id)
    log.info("Creating mapping for product category (legacy)")

    # LEGACY: Get attribute order
    attribute_order = local_sqlite_db.query(
        ATTRIBUTE_ORDER_BY_PCAT_QUERY.format(product_category_id=product_category_id)
    )
    if attribute_order.empty:
        log.warning("No attributes found for product category")
        return pd.Series()

    # LEGACY: Get attribute term counts
    attribute_term_count = local_sqlite_db.query(
        ATTRIBUTE_TERM_COUNT_BY_PCAT_QUERY.format(
            product_category_id=product_category_id
        )
    )

    # LEGACY: Get all product IDs
    all_product_ids_df = local_sqlite_db.query(
        ALL_PRODUCTS_BY_CATEGORY_QUERY.format(product_category_id=product_category_id)
    )
    if all_product_ids_df.empty:
        log.warning("No products found for product category")
        return pd.Series()

    all_product_ids = all_product_ids_df["product_id"].tolist()

    # Create mappings
    attribute_positions = dict(
        zip(attribute_order["attribute_id"], attribute_order["pcat_attribute_order"])
    )
    attribute_max_terms = dict(
        zip(
            attribute_term_count["attribute_id"],
            attribute_term_count["term_count"].fillna(0),
        )
    )

    total_attributes = len(attribute_order)
    pg_attr_ids = attribute_order["attribute_id"].tolist()
    attr_id_str = ", ".join(f"'{aid}'" for aid in pg_attr_ids)

    log.info(
        "Retrieved data", attributes=len(pg_attr_ids), products=len(all_product_ids)
    )

    # LEGACY: Query for measure data
    measure_data = local_sqlite_db.query(
        MEASURE_DATA_WITH_ORDERING_QUERY_LEGACY.format(
            product_category_id=product_category_id,
            attr_id_str=attr_id_str,
        )
    )

    # LEGACY: Query for term data
    term_data = local_sqlite_db.query(
        TERM_DATA_WITH_ORDERING_QUERY.format(
            product_category_id=product_category_id,
            attributes_by_category_table=attributes_by_category_table,
        )
    )

    # Initialize result mapping
    result_mapping = {}
    n_products = len(all_product_ids)

    # Set default values (max terms)
    for pid in all_product_ids:
        result_mapping[pid] = [0] * total_attributes
        for aid in pg_attr_ids:
            pos = attribute_positions[aid]
            max_term = attribute_max_terms.get(aid, 0)
            result_mapping[pid][pos] = max_term

    # Process measure data
    for _, row in measure_data.iterrows():
        pid = row["product_id"]
        aid = row["attribute_id"]
        term_order = row["term_order"]
        if pid in result_mapping and aid in attribute_positions:
            pos = attribute_positions[aid]
            result_mapping[pid][pos] = term_order

    # Process term data
    for _, row in term_data.iterrows():
        pid = row["product_id"]
        aid = row["attribute_id"]
        term_order = row["term_order"]
        num_terms = row["num_terms"]
        if pid in result_mapping and aid in attribute_positions:
            pos = attribute_positions[aid]
            result_mapping[pid][pos] = term_order if pd.notna(term_order) else num_terms

    log.info("Created product mappings (legacy)", count=len(result_mapping))
    return pd.Series(result_mapping)


def _process_single_category(args):
    """Process a single product category - designed for parallel execution."""
    (
        pcat_id,
        db_path,
        attributes_by_category_table,
        product_mapping_dir,
    ) = args

    # Import here to avoid circular imports in multiprocessing
    from contextdb.Engine.sqlite_database import SQLiteEngine
    from app.utils.logging import setup_logging

    # Create new database connection for this process/thread
    local_db = SQLiteEngine(db_path)
    logger = setup_logging(component_name="product_mapping_worker")
    log = logger.bind(product_category_id=pcat_id)

    try:
        log.info("Processing product category")

        mapped_products = create_product_id_mapping(
            product_category_id=pcat_id,
            local_sqlite_db=local_db,
            attributes_by_category_table=attributes_by_category_table,
            logger=logger,
        )

        if not mapped_products.empty:
            raw_term_indices = np.array(mapped_products.values.tolist(), dtype=np.int32)
            product_ids = mapped_products.index.values
            indices_pid = (raw_term_indices, product_ids)

            log.info("Saving product mapping")
            save_product_mapping(pcat_id, indices_pid, product_mapping_dir)

            # Clean up memory
            del mapped_products, product_ids, indices_pid
            gc.collect()

            log.info("Product mapping saved successfully")
            return {
                "pcat_id": pcat_id,
                "success": True,
            }
        else:
            log.warning("No products to map")
            return {"pcat_id": pcat_id, "success": True}

    except Exception as e:
        log.error("Error creating product mapping", error=str(e), exc_info=True)
        return {"pcat_id": pcat_id, "success": False, "error": str(e)[:50]}
    finally:
        # Close database connection
        if hasattr(local_db, "close"):
            local_db.close()


async def compute_product_mapping_async(
    product_mapping_dir: str,
    local_sqlite_db: "BaseDatabaseEngine",
    attributes_by_category_table: str,
    logger,
    max_workers: int = None,
    db_path: str = None,
):
    """Create product mappings for all product categories using asyncio."""
    import multiprocessing

    logger.info("Computing product mappings (async)")

    pcategory_id = local_sqlite_db.query(
        PRODUCT_CATEGORIES_FOR_PROCESSING_QUERY.format(
            attributes_by_category_table=attributes_by_category_table
        )
    )["product_category_id"].to_list()

    os.makedirs(product_mapping_dir, exist_ok=True)
    logger.info("Processing product categories (async)", count=len(pcategory_id))

    # Determine optimal number of workers
    if max_workers is None:
        cpu_count = multiprocessing.cpu_count()
        max_workers = min(len(pcategory_id), cpu_count * 2)

    logger.info("Async processing configuration", max_workers=max_workers)

    # Get database path for worker threads
    if db_path is None:
        # For SQLiteEngine, the database path is stored in the database property
        # which is inherited from BaseDatabaseEngine
        try:
            db_path = local_sqlite_db.database
        except (RuntimeError, AttributeError):
            # If database property is not accessible, try to get it from private attributes
            db_path = getattr(local_sqlite_db, "_database", None)
            if not db_path:
                logger.error("Could not determine database path from SQLiteEngine")
                raise ValueError("Database path not found in SQLiteEngine instance")

    # Create progress tracker for category processing
    from ..utils.progress_tracker import track_category_processing

    category_progress = track_category_processing(pcategory_id)

    # Create semaphore to limit concurrent workers
    semaphore = asyncio.Semaphore(max_workers)

    success_count = 0
    active_workers = set()

    async def process_category_async(pcat_id):
        """Async wrapper for processing a single category."""
        nonlocal success_count

        async with semaphore:
            worker_id = f"worker-{len(active_workers)}"
            active_workers.add(worker_id)

            logger.info(
                "Starting category processing",
                pcat_id=pcat_id,
                worker_id=worker_id,
                active_workers=len(active_workers),
            )

            try:
                # Run the blocking operation in a thread
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    _process_single_category,
                    (
                        pcat_id,
                        db_path,
                        attributes_by_category_table,
                        product_mapping_dir,
                    ),
                )

                if result["success"]:
                    success_count += 1
                    category_progress.update(
                        1,
                        cat_id=result["pcat_id"],
                        success=success_count,
                        active=len(active_workers),
                    )
                    logger.info(
                        "Category completed successfully",
                        pcat_id=result["pcat_id"],
                        worker_id=worker_id,
                    )
                else:
                    category_progress.update(
                        1,
                        cat_id=result["pcat_id"],
                        error=result.get("error", "Unknown error"),
                        success=success_count,
                        active=len(active_workers),
                    )
                    logger.error(
                        "Category processing failed",
                        pcat_id=result["pcat_id"],
                        error=result.get("error"),
                        worker_id=worker_id,
                    )

                return result

            except Exception as e:
                logger.error(
                    "Async category processing failed",
                    pcat_id=pcat_id,
                    error=str(e),
                    worker_id=worker_id,
                    exc_info=True,
                )
                category_progress.update(
                    1,
                    cat_id=pcat_id,
                    error=str(e)[:50],
                    success=success_count,
                    active=len(active_workers),
                )
                return {"pcat_id": pcat_id, "success": False, "error": str(e)[:50]}
            finally:
                active_workers.discard(worker_id)
                logger.debug(
                    "Worker finished",
                    worker_id=worker_id,
                    remaining_active=len(active_workers),
                )

    try:
        # Create tasks for all categories
        tasks = [process_category_async(pcat_id) for pcat_id in pcategory_id]

        # Process all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Count successful results
        successful_results = [
            r for r in results if isinstance(r, dict) and r.get("success", False)
        ]

    finally:
        category_progress.close()

    logger.info(
        "Async processing completed",
        success_count=len(successful_results),
        total_categories=len(pcategory_id),
    )


def compute_product_mapping_parallel(
    product_mapping_dir: str,
    local_sqlite_db: "BaseDatabaseEngine",
    attributes_by_category_table: str,
    logger,
    max_workers: int = None,
    db_path: str = None,
):
    """Create product mappings for all product categories using asyncio parallel processing."""
    return asyncio.run(
        compute_product_mapping_async(
            product_mapping_dir=product_mapping_dir,
            local_sqlite_db=local_sqlite_db,
            attributes_by_category_table=attributes_by_category_table,
            logger=logger,
            max_workers=max_workers,
            db_path=db_path,
        )
    )
