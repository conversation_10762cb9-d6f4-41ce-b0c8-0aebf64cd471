import json
import logging
import os
import psycopg2
from datetime import datetime
from pathlib import Path
from app.preprocessing.preprocessing_measure import Preprocessor

from dotenv import load_dotenv

load_dotenv()

base_dir = os.getenv("BASE_DIRECTORY")

PROCESSED_FILE = os.path.join(base_dir, "processed_today.json")
AVAILABLE_FILE = os.path.join(base_dir, "databases.json")

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("logs/preprocessing_scheduler.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

credentials = json.load(open(os.path.join(base_dir, "credentials.json")))
PG_CONN = {
    "host": credentials.get("host"),
    "port": credentials.get("port"),
    "user": credentials.get("user"),
    "password": credentials.get("password"),
}

from contextai.Embeddings.hf_embeddings import HuggingFaceEmbeddingsToolkit

embeddings = HuggingFaceEmbeddingsToolkit()


def list_databases() -> list[str]:
    try:
        conn = psycopg2.connect(**PG_CONN, dbname="postgres")
        conn.autocommit = True
        with conn.cursor() as cur:
            cur.execute(
                "SELECT datname FROM pg_database WHERE datistemplate = false and datname ~ 'gp_mm_live' order by datname ASC;"
            )
            return [row[0] for row in cur.fetchall()]
    except Exception as e:
        logger.error("Failed to list databases: %s", e)
        raise
    finally:
        if conn:
            conn.close()


def run_preprocessing(dbname: str) -> bool:
    """
    Replace this stub with real preprocessing.
    """
    try:
        logger.info("Starting preprocessing for DB: %s", dbname)
        preprocessor = Preprocessor(
            database=dbname,
            embeddings=embeddings,
            base_dir=base_dir,
            skip_id_list=[364],
        )
        preprocessor.run_preprocessing()
        logger.info("Preprocessing finished for DB: %s", dbname)
        return True
    except Exception as e:
        logger.error("Preprocessing failed for DB %s: %s", dbname, e)
        return False


def load_json(path: Path) -> list[str]:
    if path.exists():
        try:
            with open(path, "r") as f:
                return json.load(f)
        except json.JSONDecodeError:
            logger.warning("Corrupt JSON file at %s, resetting", path)
            return []
    return []


def save_json(path: Path, data: list[str]):
    tmp_path = path.with_suffix(".tmp")
    with open(tmp_path, "w") as f:
        json.dump(data, f, indent=2)
    tmp_path.replace(path)  # atomic move
    logger.debug("Saved JSON state at %s", path)


def scheduler():
    today = datetime.today().strftime("%Y_%m_%d")
    logger.info("Scheduler started for %s", today)

    dbs = list_databases()
    logger.debug("Databases available: %s", dbs)

    todays_db = dbs[-1]
    if not todays_db:
        logger.warning("No database found for today (%s)", today)
        return

    if run_preprocessing(todays_db):
        processed = load_json(PROCESSED_FILE)
        if todays_db not in processed:
            save_json(PROCESSED_FILE, {"processed": todays_db})
            logger.info("Processed DB for today saved: %s", todays_db)

    available = load_json(AVAILABLE_FILE)
    available = [db for db in available if db in dbs]  # keep only existing
    if todays_db not in available:
        available.append(todays_db)
    save_json(AVAILABLE_FILE, available)
    logger.info("Available DBs updated: %s", available)

    logger.info("Scheduler finished")


if __name__ == "__main__":
    scheduler()
