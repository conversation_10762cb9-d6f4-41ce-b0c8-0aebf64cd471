"""
SQL queries for saving reference tables locally during preprocessing.
"""

# Query to get attribute group attribute data
ATTRIBUTE_GROUP_ATTRIBUTE_QUERY = """
SELECT attribute_id, product_category_id 
FROM {cat_attribute_table}
"""

# Query to get distinct product category IDs
DISTINCT_PCAT_IDS_QUERY = """
SELECT DISTINCT(product_category_id)
FROM attribute_group_attribute
"""

# Query to get distinct attribute IDs
DISTINCT_ATTRIBUTE_IDS_QUERY = """
SELECT DISTINCT(attribute_id)
FROM attribute_group_attribute
"""

# Query to get products for specific categories
PRODUCTS_BY_CATEGORIES_QUERY = """
SELECT product_id, product_category_id
FROM public.product
WHERE product_category_id IN ({pcat_ids})
"""

# Query to get product attributes for specific categories
PRODUCT_ATTRIBUTES_BY_CATEGORIES_QUERY = """
SELECT product_id, product_attribute_id, attribute_id, measure_unit_id, measure, attribute_term_id
FROM public.product_attribute
LEFT JOIN public.product USING(product_id)
WHERE attribute_id IN ({attribute_ids}) AND product_category_id IN ({pcat_ids})
"""

# Query to get attributes by IDs
ATTRIBUTES_BY_IDS_QUERY = """
SELECT name, type, attribute_id 
FROM public.attribute
WHERE attribute_id IN ({attribute_ids})
"""

# Query to get all attribute terms
ATTRIBUTE_TERMS_QUERY = """
SELECT attribute_term_id, attribute_id, term 
FROM public.attribute_term
"""

# Query to get all measure units
MEASURE_UNITS_QUERY = """
SELECT measure_id, unit, unit_display, measure_unit_id, base, base_multiplier 
FROM public.measure_unit
"""
