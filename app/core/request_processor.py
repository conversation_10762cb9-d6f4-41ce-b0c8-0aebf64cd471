import asyncio
import psutil
import time
import json
import os
from typing import Optional, Dict, List
from fastapi import HTTPException
from app.core.inference import InferenceProductSearch
from app.utils.logging import setup_logging

logger = setup_logging(component_name="request_processor")

from dotenv import load_dotenv

load_dotenv()
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")


class SimpleConfig:
    """Simple configuration for local usage"""

    MIN_FREE_MEMORY_GB = 2  # Keep 2GB free
    MAX_CONCURRENT_REQUESTS = 5  # Maximum concurrent requests
    REQUEST_TIMEOUT = 300  # 5 minutes timeout


class RequestProcessor:
    def __init__(self, database_str: str):
        self.pg = database_str
        self.inference = InferenceProductSearch(
            database=database_str,
        )
        self.last_access_time = time.time()  # Track last access for idle cleanup

        # Simple semaphore-based concurrency control
        self._semaphore = self._create_dynamic_semaphore()
        self._active_requests = 0

        with open(
            os.path.join(BASE_DIRECTORY, database_str, "pcat_id_size.json"), "r"
        ) as f:
            self.product_category_id_size = json.load(f)

    def _create_dynamic_semaphore(self) -> asyncio.Semaphore:
        """Create semaphore with dynamic size based on available memory"""
        try:
            # Get available system memory
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)

            # Calculate max concurrent requests based on available memory
            # Keep at least MIN_FREE_MEMORY_GB free, allow ~1GB per request
            max_requests = max(
                1,
                min(
                    SimpleConfig.MAX_CONCURRENT_REQUESTS,
                    int(available_gb - SimpleConfig.MIN_FREE_MEMORY_GB),
                ),
            )

            logger.info(
                "semaphore.created",
                max_concurrent=max_requests,
                available_memory_gb=f"{available_gb:.1f}",
            )

            return asyncio.Semaphore(max_requests)
        except Exception as e:
            logger.warning("semaphore.fallback", error=str(e))
            return asyncio.Semaphore(2)  # Safe fallback

    def _check_memory_available(self) -> bool:
        """Simple memory check - ensure we have minimum free memory"""
        try:
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            return available_gb >= SimpleConfig.MIN_FREE_MEMORY_GB
        except Exception:
            return True  # If we can't check, assume it's okay

    async def start(self):
        """Start the processor - simplified, no background tasks needed"""
        logger.info("processor.started", database=self.pg)

    async def stop(self):
        """Stop the processor - simplified, no background tasks to stop"""
        logger.info("processor.stopped", database=self.pg)

    async def process_request(self, func, *args, **kwargs):
        """Simple request processing with semaphore-based concurrency control"""
        # Update last access time for idle tracking
        self.last_access_time = time.time()

        request_id = f"req_{int(time.time() * 1000)}"
        log = logger.bind(
            request_id=request_id, func_name=func.__name__, database=self.pg
        )

        # Check if we have enough memory before even trying
        if not self._check_memory_available():
            log.error("request.rejected.insufficient_memory")
            raise HTTPException(status_code=503, detail="Insufficient memory available")

        # Acquire semaphore (wait if too many concurrent requests)
        log.info("request.waiting_for_slot")
        async with self._semaphore:
            self._active_requests += 1
            log.info(
                "request.processing.started", active_requests=self._active_requests
            )

            try:
                start_time = time.time()
                result = await func(*args, **kwargs)
                process_time = time.time() - start_time

                log.info(
                    "request.processing.completed",
                    process_time=f"{process_time:.4f}s",
                    result_size=len(result) if result else 0,
                )

                return result

            except Exception as e:
                process_time = time.time() - start_time
                log.error(
                    "request.processing.failed",
                    error=str(e),
                    process_time=f"{process_time:.4f}s",
                )
                raise
            finally:
                self._active_requests -= 1
                log.info("request.completed", active_requests=self._active_requests)

    async def queue_request(self, func, *args, **kwargs):
        """Simplified request processing - just call process_request directly"""
        return await self.process_request(func, *args, **kwargs)
