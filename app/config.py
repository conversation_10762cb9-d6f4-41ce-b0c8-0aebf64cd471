"""
Application configuration settings.

This module handles all configuration loading and validation
using environment variables and default values.
"""

import os
from functools import lru_cache
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings:
    """Application settings loaded from environment variables."""

    def __init__(self):
        self.base_directory: str = os.getenv("BASE_DIRECTORY", ".")
        self.processor_idle_timeout: float = float(
            os.getenv("PROCESSOR_IDLE_TIMEOUT", "1800")
        )
        self.processor_cleaner_interval: float = float(
            os.getenv("PROCESSOR_CLEANER_INTERVAL", "60")
        )

        # Logging configuration
        self.log_dir: str = os.getenv("LOG_DIR", "./logs")
        self.log_level: str = os.getenv("LOG_LEVEL", "INFO")
        self.log_max_size: int = int(os.getenv("LOG_MAX_SIZE", "10485760"))  # 10MB
        self.log_backup_count: int = int(os.getenv("LOG_BACKUP_COUNT", "5"))


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
