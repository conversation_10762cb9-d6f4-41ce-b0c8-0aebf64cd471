"""
Product validation service for handling request validation logic.

This service handles all product query validation including:
- Product ID validation
- Scope validation
- Weight validation
- Data transformation and cleanup
"""

import os
import warnings
from typing import Dict, List, Optional

try:
    from fastapi import HTTPException
except ImportError:
    # Fallback for testing or when FastAPI is not available
    class HTTPException(Exception):
        def __init__(self, status_code: int, detail: str):
            self.status_code = status_code
            self.detail = detail
            super().__init__(detail)


from app.utils.product_validator import ProductQueryValidator
from app.utils.logging import setup_logging

logger = setup_logging(component_name="product_validation_service")


class ProductValidationService:
    """Service for validating product search requests."""

    def __init__(self, base_directory: str):
        self.base_directory = base_directory

    async def validate_product_request(
        self,
        query_product_ids: List[int],
        database: str,
        scope_product_id: Optional[List[int]] = None,
        weights: Optional[Dict[str, float]] = None,
    ) -> Dict:
        """
        Validate incoming product search request parameters.

        Args:
            query_product_ids: List of product IDs to query
            database: Database identifier
            scope_product_id: Optional list of product IDs to scope the search
            weights: Optional attribute weights for similarity calculation

        Returns:
            Dict containing validated data:
            - categories: Category ID
            - valid_product_ids: Validated product IDs
            - valid_scope_ids: Validated scope IDs
            - valid_atrids: Validated attribute weights

        Raises:
            HTTPException: If validation fails
        """
        log = logger.bind(product_ids=query_product_ids, database=database)

        try:
            if not query_product_ids:
                return {"categories": []}

            # Prepare data for validator
            validation_data = {
                "query_ids": query_product_ids,
                "scope_ids": scope_product_id or [],
                "weights": {
                    str(k): v for k, v in (weights or {}).items()
                },  # Convert keys to strings
            }

            # Get database path
            db_path = os.path.join(self.base_directory, database)

            # Create and run validator
            validator = ProductQueryValidator(validation_data, db_path)
            result = validator.validate()

            # Handle validation errors
            if result.errors:
                error_msg = "; ".join(result.errors)
                log.error("request.validation.failed", errors=result.errors)
                raise HTTPException(status_code=400, detail=error_msg)

            # Handle validation warnings
            if result.warnings:
                for warning in result.warnings:
                    log.warning("request.validation.warning", warning=warning)
                    warnings.warn(warning, RuntimeWarning)

            # Extract and transform validated data
            validated_data = self._extract_validated_data(
                validator, query_product_ids, scope_product_id, weights
            )

            log.info(
                "request.validation.success", category=validated_data["categories"]
            )
            return validated_data

        except HTTPException:
            raise
        except Exception as e:
            log.error("request.validation.failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")

    def _extract_validated_data(
        self,
        validator: ProductQueryValidator,
        query_product_ids: List[int],
        scope_product_id: Optional[List[int]],
        weights: Optional[Dict[str, float]],
    ) -> Dict:
        """Extract validated data from validator results."""
        # Extract validated product IDs
        valid_product_ids = [
            pid for pid in query_product_ids if pid in validator.product_ids
        ]

        # Extract validated scope IDs
        valid_scope_ids = None
        if scope_product_id:
            valid_scope_ids = [
                pid for pid in scope_product_id if pid in validator.product_ids
            ]

        # Get category from validator
        category = validator.category_id

        # Process weights - remove invalid attribute IDs
        valid_atrids = weights or {}
        if weights and category is not None:
            valid_atrids = self._filter_valid_weights(validator, weights, category)

        return {
            "categories": category,
            "valid_product_ids": valid_product_ids,
            "valid_scope_ids": valid_scope_ids,
            "valid_atrids": valid_atrids,
        }

    def _filter_valid_weights(
        self,
        validator: ProductQueryValidator,
        weights: Dict[str, float],
        category: int,
    ) -> Dict[str, float]:
        """Filter weights to only include valid attribute IDs for the category."""
        category_str = str(category)
        if category_str in validator.attribute_id_by_category:
            valid_attr_ids = set(
                map(str, validator.attribute_id_by_category[category_str])
            )
            return {k: v for k, v in weights.items() if str(k) in valid_attr_ids}
        return weights
