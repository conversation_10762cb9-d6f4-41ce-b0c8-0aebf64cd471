import logging
import structlog
import os
from logging.handlers import RotatingFileHandler


def setup_logging(log_level=logging.INFO, component_name="app"):
    """Configure structured logging that works in both local and Docker environments.

    Args:
        log_level: The logging level to use (default: INFO)
        component_name: Name of the component for log file naming (default: "app")

    Returns:
        A configured logger instance
    """
    # Get log configuration from environment variables
    log_dir = os.getenv("LOG_DIR", "./logs")
    log_file = os.path.join(log_dir, f"{component_name}.log")
    max_bytes = int(os.getenv("LOG_MAX_SIZE", "10485760"))  # 10MB default
    backup_count = int(os.getenv("LOG_BACKUP_COUNT", "5"))

    # Ensure log directory exists
    os.makedirs(log_dir, exist_ok=True)

    # Create handlers
    console_handler = logging.StreamHandler()
    file_handler = RotatingFileHandler(
        log_file, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
    )

    # Configure basic logging
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[console_handler, file_handler],
        force=True,  # Override any existing configuration
    )

    # Suppress noisy loggers
    logging.getLogger("watchfiles.main").setLevel(logging.WARNING)

    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.add_log_level,
            structlog.stdlib.add_logger_name,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    return structlog.get_logger(component_name)
