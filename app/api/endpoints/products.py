"""
Product search API endpoints.

This module contains FastAPI endpoints for product similarity search operations.
All business logic has been moved to service layers for better separation of concerns.
"""

try:
    from fastapi import APIRouter, HTTPException
    from fastapi.responses import JSONResponse
    from fastapi.encoders import jsonable_encoder
except ImportError:
    # Fallback for when FastAPI is not available
    class APIRouter:
        def post(self, path: str):
            def decorator(func):
                return func

            return decorator

        def get(self, path: str):
            def decorator(func):
                return func

            return decorator

    class HTTPException(Exception):
        def __init__(self, status_code: int, detail: str):
            self.status_code = status_code
            self.detail = detail
            super().__init__(detail)

    class JSONResponse:
        def __init__(self, content, status_code: int = 200):
            self.content = content
            self.status_code = status_code

    def jsonable_encoder(obj):
        return obj


from app.schemas.product_models import (
    ProductSearchRequest,
    ProductSpecsSearchRequest,
)
from app.services.product_validation_service import ProductValidationService
from app.services.product_search_service import ProductSearchService
from app.services.database_service import DatabaseService
from app.utils.logging import setup_logging
from app.config import get_settings

# Initialize services
settings = get_settings()
router = APIRouter()
logger = setup_logging(component_name="products_api")

# Service instances
validation_service = ProductValidationService(settings.base_directory)
search_service = ProductSearchService()
database_service = DatabaseService(settings.base_directory)


@router.post("/get-similar-products-by-product-id")
async def get_similar_products(request: ProductSearchRequest) -> JSONResponse:
    """
    Search for similar products using existing product IDs.

    Args:
        request: Product search request containing query parameters

    Returns:
        JSON response with search results

    Raises:
        HTTPException: If validation or search fails
    """
    try:
        # Resolve database if empty
        database = request.database
        if not database:
            database = database_service.resolve_default_database()

        # Validate request parameters
        validation_result = await validation_service.validate_product_request(
            query_product_ids=request.query_product_ids,
            database=database,
            scope_product_id=request.scope_product_id,
            weights=request.weights,
        )

        # Perform search using validated data
        search_results = await search_service.search_similar_products_by_id(
            query_product_ids=validation_result["valid_product_ids"],
            category=validation_result["categories"],
            database=database,
            weights=validation_result["valid_atrids"],
            top_k=request.top_k,
            scope_product_ids=validation_result["valid_scope_ids"],
        )

        return JSONResponse(content=jsonable_encoder(search_results), status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("get_similar_products.failed", error=str(e))
        raise HTTPException(
            status_code=500, detail=f"Search operation failed: {str(e)}"
        )


@router.post("/get-similar-products-by-specs")
async def get_similar_products_by_specs(
    request: ProductSpecsSearchRequest,
) -> JSONResponse:
    """
    Search for similar products using product specifications.

    Args:
        request: Product specs search request containing query parameters

    Returns:
        JSON response with search results

    Raises:
        HTTPException: If validation or search fails
    """
    try:
        # Resolve database if empty
        database = request.database
        if not database:
            database = database_service.resolve_default_database()

        # Perform search using specs
        search_results = await search_service.search_similar_products_by_specs(
            specs=request.specs,
            product_category_id=request.product_category_id,
            database=database,
            weights=request.weights,
            top_k=request.top_k,
            scope_query=request.scope_query,
        )

        return JSONResponse(content=jsonable_encoder(search_results), status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("get_similar_products_by_specs.failed", error=str(e))
        raise HTTPException(
            status_code=500, detail=f"Specs search operation failed: {str(e)}"
        )


@router.get("/get-available-databases")
async def get_available_databases() -> JSONResponse:
    """
    Get list of available databases.

    Returns:
        JSON response with available databases

    Raises:
        HTTPException: If database retrieval fails
    """
    try:
        databases = database_service.get_available_databases()
        return JSONResponse(content=databases, status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("get_available_databases.failed", error=str(e))
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve databases: {str(e)}"
        )


@router.get("/get-specific-search-scope-databases")
async def get_specific_search_scope_databases() -> JSONResponse:
    """
    Get specific search scope databases.

    This endpoint is not yet implemented.

    Returns:
        JSON response indicating not implemented

    Raises:
        HTTPException: Always raises 501 Not Implemented
    """
    raise HTTPException(
        status_code=501,
        detail="Specific search scope databases endpoint not implemented yet",
    )
