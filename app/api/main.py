# app/api/main.py
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import products
from app.core.processor_registry import registry

import os
from dotenv import load_dotenv

load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    # Startup
    await registry.start()

    yield

    # Shutdown
    await registry.stop()


class ProductLineup:
    def __init__(self):
        self.app = FastAPI(lifespan=lifespan)
        self.setup_routers()
        self.setup_cors()

    def setup_routers(self):
        self.app.include_router(products.router)

    def setup_cors(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allow_headers=["*"],
        )


def create_app():
    product_lineup = ProductLineup()
    return product_lineup.app


# Create the app instance for ASGI servers
app = create_app()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
    )
